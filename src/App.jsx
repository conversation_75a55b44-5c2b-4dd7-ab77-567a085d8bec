import React, { useState, useEffect, useCallback } from 'react';
import { initializeApp } from 'firebase/app';
import { getAuth, signInAnonymously, signInWithCustomToken, onAuthStateChanged } from 'firebase/auth';
import { getFirestore, collection, query, onSnapshot, doc, addDoc, updateDoc, deleteDoc, serverTimestamp, where } from 'firebase/firestore';

// Import utilities and context
import { formatCurrency, formatDate } from './utils/formatters';
import { AuthContext } from './context/AuthContext';

// Import modal components
import ConfirmationModal from './components/modals/ConfirmationModal';
import MessageBox from './components/modals/MessageBox';
import HotelModal from './components/modals/HotelModal';
import RoomModal from './components/modals/RoomModal';
import ReservationModal from './components/modals/ReservationModal';
import ReservationDetailModal from './components/modals/ReservationDetailModal';

// Import main feature components
import Dashboard from './components/Dashboard';
import Reservations from './components/Reservations';
import Rooms from './components/Rooms';
import Finance from './components/Finance';

// Main App Component
const App = () => {
 // State for active navigation tab
 const [activeTab, setActiveTab] = useState('dashboard');
 // Firebase related states
 const [app, setApp] = useState(null);
 const [db, setDb] = useState(null);
 const [auth, setAuth] = useState(null);
 const [userId, setUserId] = useState(null);
 const [isAuthReady, setIsAuthReady] = useState(false); // Tracks if Firebase auth is initialized

 // Hotel management states
 const [hotels, setHotels] = useState([]);
 const [selectedHotelId, setSelectedHotelId] = useState(null);
 const [showHotelModal, setShowHotelModal] = useState(false);
 const [currentHotel, setCurrentHotel] = useState(null); // Used for editing hotel

 // Data states for rooms, reservations, and finances
 const [rooms, setRooms] = useState([]);
 const [reservations, setReservations] = useState([]);
 const [finances, setFinances] = useState([]);

 // Modals visibility states
 const [showRoomModal, setShowRoomModal] = useState(false);
 const [currentRoom, setCurrentRoom] = useState(null); // Used for editing room
 const [showReservationModal, setShowReservationModal] = useState(false);
 const [currentReservation, setCurrentReservation] = useState(null); // Used for editing reservation
 const [showReservationDetailModal, setShowReservationDetailModal] = useState(false); // New: for invoice/detail view
 const [showConfirmModal, setShowConfirmModal] = useState(false);
 const [confirmAction, setConfirmAction] = useState(null); // Function to execute on confirmation
 const [confirmMessage, setConfirmMessage] = useState('');

 // Custom alert/message box state
 const [messageBox, setMessageBox] = useState({ visible: false, message: '', type: 'info' });

 // Function to show custom message box
 const showMessage = (message, type = 'info', duration = 3000) => {
   setMessageBox({ visible: true, message, type });
   setTimeout(() => {
     setMessageBox({ visible: false, message: '', type: 'info' });
   }, duration);
 };

 // --- Firebase Initialization and Authentication ---
 useEffect(() => {
   try {
     const firebaseConfig = typeof __firebase_config !== 'undefined' ? JSON.parse(__firebase_config) : {};
     const initializedApp = initializeApp(firebaseConfig);
     const firestoreDb = getFirestore(initializedApp);
     const firebaseAuth = getAuth(initializedApp);

     setApp(initializedApp);
     setDb(firestoreDb);
     setAuth(firebaseAuth);

     const unsubscribe = onAuthStateChanged(firebaseAuth, async (user) => {
       if (user) {
         setUserId(user.uid);
         showMessage('Autentikasi berhasil.', 'success');
       } else {
         // Sign in anonymously if no user and no custom token provided
         if (typeof __initial_auth_token === 'undefined' || !__initial_auth_token) {
           try {
             await signInAnonymously(firebaseAuth);
             setUserId(firebaseAuth.currentUser?.uid || crypto.randomUUID());
             showMessage('Masuk secara anonim berhasil.', 'info');
           } catch (anonError) {
             console.error("Kesalahan saat masuk secara anonim:", anonError);
             showMessage('Gagal masuk secara anonim.', 'error');
             setUserId(crypto.randomUUID()); // Fallback to a random ID if anonymous sign-in fails
           }
         }
       }
       setIsAuthReady(true); // Auth state is now ready
     });

     // Handle custom token if available
     if (typeof __initial_auth_token !== 'undefined' && __initial_auth_token) {
       signInWithCustomToken(firebaseAuth, __initial_auth_token)
         .then((userCredential) => {
           setUserId(userCredential.user.uid);
           showMessage('Masuk dengan token kustom berhasil.', 'success');
         })
         .catch((error) => {
           console.error("Kesalahan saat masuk dengan token kustom:", error);
           showMessage('Gagal masuk dengan token kustom. Mencoba masuk secara anonim...', 'error');
           // Fallback to anonymous if custom token fails
           signInAnonymously(firebaseAuth)
             .then(() => {
               setUserId(firebaseAuth.currentUser?.uid || crypto.randomUUID());
               showMessage('Masuk secara anonim berhasil sebagai pengganti.', 'info');
             })
             .catch(err => {
               console.error("Kesalahan saat masuk secara anonim:", err);
               showMessage('Gagal masuk secara anonim.', 'error');
             });
         })
         .finally(() => setIsAuthReady(true));
     }

     return () => unsubscribe(); // Cleanup auth listener on component unmount
   } catch (e) {
     console.error("Gagal menginisialisasi Firebase:", e);
     showMessage('Gagal menginisialisasi Firebase.', 'error');
     setIsAuthReady(true); // Still set to true to avoid infinite loading, but data might not load
   }
 }, []); // Run once on component mount

 // --- Firestore Data Listeners ---
 useEffect(() => {
   if (!db || !userId || !isAuthReady) return; // Wait for Firebase and Auth to be ready

   const appId = typeof __app_id !== 'undefined' ? __app_id : 'default-app-id';
   const userPrivatePath = `artifacts/${appId}/users/${userId}`;

   // Hotels Listener
   const hotelsCollectionRef = collection(db, `${userPrivatePath}/hotels`);
   const unsubscribeHotels = onSnapshot(hotelsCollectionRef, (snapshot) => {
     const hotelsData = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
     setHotels(hotelsData);
     // Automatically select the first hotel if none is selected
     if (!selectedHotelId && hotelsData.length > 0) {
       setSelectedHotelId(hotelsData[0].id);
     } else if (hotelsData.length === 0) {
       setSelectedHotelId(null);
     }
   }, (error) => {
     console.error("Kesalahan saat mengambil data hotel:", error);
     showMessage('Gagal memuat data hotel.', 'error');
   });

   return () => {
     unsubscribeHotels();
   };
 }, [db, userId, isAuthReady, selectedHotelId]); // Re-run if db, userId, isAuthReady, or selectedHotelId changes

 // Listener for rooms, reservations, and finances based on selected hotel
 useEffect(() => {
   if (!db || !userId || !isAuthReady || !selectedHotelId) {
     setRooms([]);
     setReservations([]);
     setFinances([]);
     return; // Clear data if no hotel is selected or not ready
   }

   const appId = typeof __app_id !== 'undefined' ? __app_id : 'default-app-id';
   const hotelDataPath = `artifacts/${appId}/users/${userId}/hotels/${selectedHotelId}`;

   // Rooms Listener
   const roomsCollectionRef = collection(db, `${hotelDataPath}/rooms`);
   const unsubscribeRooms = onSnapshot(roomsCollectionRef, (snapshot) => {
     const roomsData = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
     setRooms(roomsData);
   }, (error) => {
     console.error("Kesalahan saat mengambil data kamar:", error);
     showMessage('Gagal memuat data kamar.', 'error');
   });

   // Reservations Listener
   const reservationsCollectionRef = collection(db, `${hotelDataPath}/reservations`);
   const unsubscribeReservations = onSnapshot(reservationsCollectionRef, (snapshot) => {
     const reservationsData = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
     setReservations(reservationsData);
   }, (error) => {
     console.error("Kesalahan saat mengambil data reservasi:", error);
     showMessage('Gagal memuat data reservasi.', 'error');
   });

   // Finances Listener
   const financesCollectionRef = collection(db, `${hotelDataPath}/finances`);
   const unsubscribeFinances = onSnapshot(financesCollectionRef, (snapshot) => {
     const financesData = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
     setFinances(financesData);
   }, (error) => {
     console.error("Kesalahan saat mengambil data keuangan:", error);
     showMessage('Gagal memuat data keuangan.', 'error');
   });

   return () => {
     unsubscribeRooms();
     unsubscribeReservations();
     unsubscribeFinances();
   };
 }, [db, userId, isAuthReady, selectedHotelId]); // Re-run if db, userId, isAuthReady, or selectedHotelId changes

 // --- CRUD Operations for Hotels ---
 const addHotel = async (hotelData) => {
   if (!db || !userId) { showMessage('Basis data tidak tersedia.', 'error'); return; }
   try {
     const appId = typeof __app_id !== 'undefined' ? __app_id : 'default-app-id';
     const docRef = await addDoc(collection(db, `artifacts/${appId}/users/${userId}/hotels`), {
       ...hotelData,
       createdAt: serverTimestamp(),
       userId: userId,
     });
     showMessage('Hotel berhasil ditambahkan!', 'success');
     setSelectedHotelId(docRef.id); // Select the new hotel
     setShowHotelModal(false);
   } catch (e) {
     console.error("Kesalahan saat menambahkan hotel: ", e);
     showMessage('Gagal menambahkan hotel.', 'error');
   }
 };

 const updateHotel = async (id, hotelData) => {
   if (!db || !userId) { showMessage('Basis data tidak tersedia.', 'error'); return; }
   try {
     const appId = typeof __app_id !== 'undefined' ? __app_id : 'default-app-id';
     await updateDoc(doc(db, `artifacts/${appId}/users/${userId}/hotels`, id), {
       ...hotelData,
       updatedAt: serverTimestamp(),
     });
     showMessage('Hotel berhasil diperbarui!', 'success');
     setShowHotelModal(false);
   } catch (e) {
     console.error("Kesalahan saat memperbarui hotel: ", e);
     showMessage('Gagal memperbarui hotel.', 'error');
   }
 };

 const deleteHotel = async (id) => {
   if (!db || !userId) { showMessage('Basis data tidak tersedia.', 'error'); return; }
   try {
     const appId = typeof __app_id !== 'undefined' ? __app_id : 'default-app-id';
     // WARNING: Deleting a hotel document currently does NOT automatically delete its sub-collections (rooms, reservations, finances).
     // These will become "orphaned" in Firestore. For a production app, consider using Firebase Cloud Functions
     // to implement cascading deletes when a hotel document is deleted.
     await deleteDoc(doc(db, `artifacts/${appId}/users/${userId}/hotels`, id));
     showMessage('Hotel berhasil dihapus! (Catatan: data terkait kamar/reservasi/keuangan mungkin masih ada)', 'success', 5000);
     setShowConfirmModal(false); // Close confirm modal
     if (selectedHotelId === id) {
       setSelectedHotelId(hotels.length > 1 ? hotels[0].id : null); // Select another hotel or clear
     }
   } catch (e) {
     console.error("Kesalahan saat menghapus hotel: ", e);
     showMessage('Gagal menghapus hotel.', 'error');
   }
 };


 // --- CRUD Operations for Rooms ---
 const addRoom = async (roomData) => {
   if (!db || !userId || !selectedHotelId) { showMessage('Hotel belum dipilih atau basis data tidak tersedia.', 'error'); return; }
   try {
     const appId = typeof __app_id !== 'undefined' ? __app_id : 'default-app-id';
     const docRef = await addDoc(collection(db, `artifacts/${appId}/users/${userId}/hotels/${selectedHotelId}/rooms`), {
       ...roomData,
       createdAt: serverTimestamp(),
       userId: userId,
       hotelId: selectedHotelId,
     });
     showMessage('Kamar berhasil ditambahkan!', 'success');
     console.log("Kamar ditambahkan dengan ID: ", docRef.id);
     setShowRoomModal(false);
   } catch (e) {
     console.error("Kesalahan saat menambahkan kamar: ", e);
     showMessage('Gagal menambahkan kamar.', 'error');
   }
 };

 const updateRoom = async (id, roomData) => {
   if (!db || !userId || !selectedHotelId) { showMessage('Hotel belum dipilih atau basis data tidak tersedia.', 'error'); return; }
   try {
     const appId = typeof __app_id !== 'undefined' ? __app_id : 'default-app-id';
     await updateDoc(doc(db, `artifacts/${appId}/users/${userId}/hotels/${selectedHotelId}/rooms`, id), {
       ...roomData,
       updatedAt: serverTimestamp(),
     });
     showMessage('Kamar berhasil diperbarui!', 'success');
     console.log("Kamar diperbarui dengan ID: ", id);
     setShowRoomModal(false);
   } catch (e) {
     console.error("Kesalahan saat memperbarui kamar: ", e);
     showMessage('Gagal memperbarui kamar.', 'error');
   }
 };

 const deleteRoom = async (id) => {
   if (!db || !userId || !selectedHotelId) { showMessage('Hotel belum dipilih atau basis data tidak tersedia.', 'error'); return; }
   try {
     const appId = typeof __app_id !== 'undefined' ? __app_id : 'default-app-id';
     await deleteDoc(doc(db, `artifacts/${appId}/users/${userId}/hotels/${selectedHotelId}/rooms`, id));
     showMessage('Kamar berhasil dihapus!', 'success');
     console.log("Kamar dihapus dengan ID: ", id);
     setShowConfirmModal(false); // Close confirm modal
   } catch (e) {
     console.error("Kesalahan saat menghapus kamar: ", e);
     showMessage('Gagal menghapus kamar.', 'error');
   }
 };

 // --- CRUD Operations for Reservations ---
 const addReservation = async (reservationData) => {
   if (!db || !userId || !selectedHotelId) { showMessage('Hotel belum dipilih atau basis data tidak tersedia.', 'error'); return; }
   try {
     const appId = typeof __app_id !== 'undefined' ? __app_id : 'default-app-id';
     await addDoc(collection(db, `artifacts/${appId}/users/${userId}/hotels/${selectedHotelId}/reservations`), {
       ...reservationData,
       checkInDate: new Date(reservationData.checkInDate), // Convert string to Date object
       checkOutDate: new Date(reservationData.checkOutDate), // Convert string to Date object
       createdAt: serverTimestamp(),
       userId: userId,
       hotelId: selectedHotelId,
     });
     showMessage('Reservasi berhasil ditambahkan!', 'success');
     setShowReservationModal(false);
   } catch (e) {
     console.error("Kesalahan saat menambahkan reservasi: ", e);
     showMessage('Gagal menambahkan reservasi.', 'error');
   }
 };

 const updateReservation = async (id, reservationData) => {
   if (!db || !userId || !selectedHotelId) { showMessage('Hotel belum dipilih atau basis data tidak tersedia.', 'error'); return; }
   try {
     const appId = typeof __app_id !== 'undefined' ? __app_id : 'default-app-id';
     await updateDoc(doc(db, `artifacts/${appId}/users/${userId}/hotels/${selectedHotelId}/reservations`, id), {
       ...reservationData,
       checkInDate: new Date(reservationData.checkInDate), // Ensure Date objects
       checkOutDate: new Date(reservationData.checkOutDate), // Ensure Date objects
       updatedAt: serverTimestamp(),
     });
     showMessage('Reservasi berhasil diperbarui!', 'success');
     setShowReservationModal(false);
   } catch (e) {
     console.error("Kesalahan saat memperbarui reservasi: ", e);
     showMessage('Gagal memperbarui reservasi.', 'error');
   }
 };

 const deleteReservation = async (id) => {
   if (!db || !userId || !selectedHotelId) { showMessage('Hotel belum dipilih atau basis data tidak tersedia.', 'error'); return; }
   try {
     const appId = typeof __app_id !== 'undefined' ? __app_id : 'default-app-id';
     await deleteDoc(doc(db, `artifacts/${appId}/users/${userId}/hotels/${selectedHotelId}/reservations`, id));
     showMessage('Reservasi berhasil dihapus!', 'success');
     setShowConfirmModal(false); // Close confirm modal
   } catch (e) {
     console.error("Kesalahan saat menghapus reservasi: ", e);
     showMessage('Gagal menghapus reservasi.', 'error');
   }
 };

 // --- CRUD Operations for Finances (simplified) ---
 const addFinanceEntry = async (entryData) => {
   if (!db || !userId || !selectedHotelId) { showMessage('Hotel belum dipilih atau basis data tidak tersedia.', 'error'); return; }
   try {
     const appId = typeof __app_id !== 'undefined' ? __app_id : 'default-app-id';
     await addDoc(collection(db, `artifacts/${appId}/users/${userId}/hotels/${selectedHotelId}/finances`), {
       ...entryData,
       date: new Date(entryData.date), // Convert string to Date object
       createdAt: serverTimestamp(),
       userId: userId,
       hotelId: selectedHotelId,
     });
     showMessage('Entri keuangan berhasil ditambahkan!', 'success');
   } catch (e) {
     console.error("Kesalahan saat menambahkan entri keuangan: ", e);
     showMessage('Gagal menambahkan entri keuangan.', 'error');
   }
 };

 const deleteFinanceEntry = async (id) => {
   if (!db || !userId || !selectedHotelId) { showMessage('Hotel belum dipilih atau basis data tidak tersedia.', 'error'); return; }
   try {
     const appId = typeof __app_id !== 'undefined' ? __app_id : 'default-app-id';
     await deleteDoc(doc(db, `artifacts/${appId}/users/${userId}/hotels/${selectedHotelId}/finances`, id));
     showMessage('Entri keuangan berhasil dihapus!', 'success');
     setShowConfirmModal(false);
   } catch (e) {
     console.error("Kesalahan saat menghapus entri keuangan: ", e);
     showMessage('Gagal menghapus entri keuangan.', 'error');
   }
 };
















 // --- Main Layout ---
 if (!isAuthReady) {
   return (
     <div className="flex items-center justify-center min-h-screen bg-gray-100">
       <div className="text-center text-gray-700">
         <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500 mb-4"></div>
         <p className="text-xl font-semibold">Memuat aplikasi dan mengautentikasi...</p>
       </div>
     </div>
   );
 }

 return (
   <AuthContext.Provider value={{ userId, db, auth }}>
     <div className="font-inter antialiased flex flex-col min-h-screen bg-gray-100 text-gray-900">
       {/* Font Awesome CDN for icons */}
       <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" />

       {/* Global styles for Inter font */}
       <style>
         {`
         @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
         body { font-family: 'Inter', sans-serif; }
         .scroll-container {
           -webkit-overflow-scrolling: touch;
         }
         /* Custom scrollbar for better UX */
         .scroll-container::-webkit-scrollbar {
             width: 8px;
             height: 8px;
         }
         .scroll-container::-webkit-scrollbar-track {
             background: #f1f1f1;
             border-radius: 10px;
         }
         .scroll-container::-webkit-scrollbar-thumb {
             background: #888;
             border-radius: 10px;
         }
         .scroll-container::-webkit-scrollbar-thumb:hover {
             background: #555;
         }
         `}
       </style>

       {/* Header / Top Navigation for small screens */}
       <header className="bg-white shadow-sm md:hidden sticky top-0 z-40">
         <div className="container mx-auto px-4 py-3 flex items-center justify-between">
           <h1 className="text-xl font-bold text-blue-700">Arrahmah Hotel Manajemen</h1>
           <div className="flex items-center">
             <span className="text-sm text-gray-600 mr-2 break-all">ID Pengguna: {userId}</span>
             {/* Hotel Selector for mobile */}
             <select
               value={selectedHotelId || ''}
               onChange={(e) => setSelectedHotelId(e.target.value)}
               className="ml-2 p-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
             >
               <option value="">Pilih Hotel</option>
               {hotels.map(hotel => (
                 <option key={hotel.id} value={hotel.id}>{hotel.name}</option>
               ))}
             </select>
             <button
               onClick={() => { setCurrentHotel(null); setShowHotelModal(true); }}
               className="ml-2 p-2 rounded-md text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
               aria-label="Tambah Hotel"
             >
               <i className="fas fa-plus"></i>
             </button>
           </div>
         </div>
         {/* Mobile bottom navigation for easy access */}
         <nav className="bg-white border-t border-gray-200 flex justify-around py-2">
           <button
             onClick={() => setActiveTab('dashboard')}
             className={`flex-1 flex flex-col items-center p-2 text-sm font-medium rounded-md transition-colors duration-200
               ${activeTab === 'dashboard' ? 'text-blue-700 bg-blue-50' : 'text-gray-600 hover:bg-gray-50'}`}
           >
             <i className="fas fa-chart-line text-lg mb-1"></i>
             Dashboard
           </button>
           <button
             onClick={() => setActiveTab('reservations')}
             className={`flex-1 flex flex-col items-center p-2 text-sm font-medium rounded-md transition-colors duration-200
               ${activeTab === 'reservations' ? 'text-blue-700 bg-blue-50' : 'text-gray-600 hover:bg-gray-50'}`}
           >
             <i className="fas fa-book-bed text-lg mb-1"></i>
             Reservasi
           </button>
           <button
             onClick={() => setActiveTab('rooms')}
             className={`flex-1 flex flex-col items-center p-2 text-sm font-medium rounded-md transition-colors duration-200
               ${activeTab === 'rooms' ? 'text-blue-700 bg-blue-50' : 'text-gray-600 hover:bg-gray-50'}`}
           >
             <i className="fas fa-hotel text-lg mb-1"></i>
             Kamar
           </button>
           <button
             onClick={() => setActiveTab('finance')}
             className={`flex-1 flex flex-col items-center p-2 text-sm font-medium rounded-md transition-colors duration-200
               ${activeTab === 'finance' ? 'text-blue-700 bg-blue-50' : 'text-gray-600 hover:bg-gray-50'}`}
           >
             <i className="fas fa-dollar-sign text-lg mb-1"></i>
             Keuangan
           </button>
         </nav>
       </header>

       <div className="flex flex-1 overflow-hidden">
         {/* Sidebar for Desktop */}
         <aside className="hidden md:flex flex-col w-64 bg-white shadow-lg border-r border-gray-200 p-6">
           <div className="flex items-center mb-6">
             <i className="fas fa-building text-3xl text-blue-600 mr-3"></i>
             <h2 className="text-2xl font-extrabold text-gray-900">Arrahmah PMS</h2>
           </div>

           {/* Hotel Selector and Management */}
           <div className="mb-6">
             <label htmlFor="hotel-select" className="block text-sm font-medium text-gray-700 mb-2">Pilih Hotel:</label>
             <select
               id="hotel-select"
               value={selectedHotelId || ''}
               onChange={(e) => setSelectedHotelId(e.target.value)}
               className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
             >
               <option value="">-- Pilih Hotel --</option>
               {hotels.map(hotel => (
                 <option key={hotel.id} value={hotel.id}>{hotel.name}</option>
               ))}
             </select>
             <div className="mt-3 flex flex-wrap gap-2">
               <button
                 onClick={() => { setCurrentHotel(null); setShowHotelModal(true); }}
                 className="flex-1 px-3 py-2 bg-green-500 text-white text-sm rounded-md hover:bg-green-600 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-green-400 focus:ring-opacity-75"
               >
                 <i className="fas fa-plus mr-1"></i> Tambah Hotel
               </button>
               {selectedHotelId && (
                 <button
                   onClick={() => {
                     const hotelToEdit = hotels.find(h => h.id === selectedHotelId);
                     if (hotelToEdit) {
                       setCurrentHotel(hotelToEdit);
                       setShowHotelModal(true);
                     }
                   }}
                   className="flex-1 px-3 py-2 bg-yellow-500 text-white text-sm rounded-md hover:bg-yellow-600 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:ring-opacity-75"
                 >
                   <i className="fas fa-edit mr-1"></i> Edit Hotel
                 </button>
               )}
               {selectedHotelId && (
                 <button
                   onClick={() => {
                     setConfirmMessage('Apakah Anda yakin ingin menghapus hotel ini? Tindakan ini tidak akan menghapus data kamar, reservasi, dan keuangan terkait secara otomatis. Silakan baca catatan penting di bawah.');
                     setConfirmAction(() => () => deleteHotel(selectedHotelId));
                     setShowConfirmModal(true);
                   }}
                   className="flex-1 px-3 py-2 bg-red-500 text-white text-sm rounded-md hover:bg-red-600 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-400 focus:ring-opacity-75"
                 >
                   <i className="fas fa-trash-alt mr-1"></i> Hapus Hotel
                 </button>
               )}
             </div>
           </div>

           <nav className="flex-1 space-y-2">
             <button
               onClick={() => setActiveTab('dashboard')}
               className={`w-full flex items-center p-3 rounded-lg text-lg font-medium transition-colors duration-200
                 ${activeTab === 'dashboard' ? 'bg-blue-600 text-white shadow-md' : 'text-gray-700 hover:bg-gray-100 hover:text-blue-600'}`}
             >
               <i className="fas fa-chart-line mr-3 text-xl"></i>
               Dashboard
             </button>
             <button
               onClick={() => setActiveTab('reservations')}
               className={`w-full flex items-center p-3 rounded-lg text-lg font-medium transition-colors duration-200
                 ${activeTab === 'reservations' ? 'bg-blue-600 text-white shadow-md' : 'text-gray-700 hover:bg-gray-100 hover:text-blue-600'}`}
             >
               <i className="fas fa-book-bed mr-3 text-xl"></i>
               Reservasi
             </button>
             <button
               onClick={() => setActiveTab('rooms')}
               className={`w-full flex items-center p-3 rounded-lg text-lg font-medium transition-colors duration-200
                 ${activeTab === 'rooms' ? 'bg-blue-600 text-white shadow-md' : 'text-gray-700 hover:bg-gray-100 hover:text-blue-600'}`}
             >
               <i className="fas fa-hotel mr-3 text-xl"></i>
               Kamar
             </button>
             <button
               onClick={() => setActiveTab('finance')}
               className={`w-full flex items-center p-3 rounded-lg text-lg font-medium transition-colors duration-200
                 ${activeTab === 'finance' ? 'bg-blue-600 text-white shadow-md' : 'text-gray-700 hover:bg-gray-100 hover:text-blue-600'}`}
             >
               <i className="fas fa-dollar-sign mr-3 text-xl"></i>
               Keuangan
             </button>
           </nav>
           <div className="mt-8 text-sm text-gray-600 p-2 border-t border-gray-200 pt-4">
             <p className="font-semibold">ID Pengguna:</p>
             <p className="break-all">{userId}</p>
           </div>
         </aside>

         {/* Main Content Area */}
         <main className="flex-1 overflow-y-auto scroll-container">
           {activeTab === 'dashboard' && (
             <Dashboard
               rooms={rooms}
               reservations={reservations}
               finances={finances}
               hotels={hotels}
               selectedHotelId={selectedHotelId}
               setCurrentHotel={setCurrentHotel}
               setShowHotelModal={setShowHotelModal}
             />
           )}
           {activeTab === 'reservations' && (
             <Reservations
               reservations={reservations}
               rooms={rooms}
               hotels={hotels}
               selectedHotelId={selectedHotelId}
               addReservation={addReservation}
               updateReservation={updateReservation}
               deleteReservation={deleteReservation}
               showMessage={showMessage}
               setConfirmMessage={setConfirmMessage}
               setConfirmAction={setConfirmAction}
               setShowConfirmModal={setShowConfirmModal}
             />
           )}
           {activeTab === 'rooms' && (
             <Rooms
               rooms={rooms}
               selectedHotelId={selectedHotelId}
               addRoom={addRoom}
               updateRoom={updateRoom}
               deleteRoom={deleteRoom}
               showMessage={showMessage}
               setConfirmMessage={setConfirmMessage}
               setConfirmAction={setConfirmAction}
               setShowConfirmModal={setShowConfirmModal}
             />
           )}
           {activeTab === 'finance' && (
             <Finance
               finances={finances}
               selectedHotelId={selectedHotelId}
               addFinanceEntry={addFinanceEntry}
               deleteFinanceEntry={deleteFinanceEntry}
               showMessage={showMessage}
               setConfirmMessage={setConfirmMessage}
               setConfirmAction={setConfirmAction}
               setShowConfirmModal={setShowConfirmModal}
             />
           )}
         </main>
       </div>

       {/* Modals and Message Box */}
       <HotelModal
         hotel={currentHotel}
         onClose={() => setShowHotelModal(false)}
         onSave={(id, data) => id ? updateHotel(id, data) : addHotel(data)}
         showMessage={showMessage}
       />
       <ConfirmationModal
         visible={showConfirmModal}
         message={confirmMessage}
         onConfirm={() => {
           if (confirmAction) confirmAction();
           setShowConfirmModal(false);
           setConfirmAction(null);
         }}
         onCancel={() => setShowConfirmModal(false)}
       />
       <MessageBox
         message={messageBox.message}
         type={messageBox.type}
         visible={messageBox.visible}
       />
     </div>
   </AuthContext.Provider>
 );
};

export default App;