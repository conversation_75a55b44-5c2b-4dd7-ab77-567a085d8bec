import React, { useState } from 'react';
import { formatCurrency, formatDate } from '../utils/formatters';

const Finance = ({
  finances,
  selectedHotelId,
  addFinanceEntry,
  deleteFinanceEntry,
  showMessage,
  setConfirmMessage,
  setConfirmAction,
  setShowConfirmModal
}) => {
  const [newEntry, setNewEntry] = useState({
    date: new Date().toISOString().split('T')[0],
    type: 'revenue',
    description: '',
    amount: 0,
  });
  const [filterType, setFilterType] = useState('all');
  const [filterStartDate, setFilterStartDate] = useState('');
  const [filterEndDate, setFilterEndDate] = useState('');

  const handleEntryChange = (e) => {
    const { name, value, type } = e.target;
    setNewEntry(prev => ({
      ...prev,
      [name]: type === 'number' ? parseFloat(value) || 0 : value
    }));
  };

  const handleAddEntry = (e) => {
    e.preventDefault();
    if (!newEntry.description.trim()) {
      showMessage('Deskripsi tidak boleh kosong.', 'error');
      return;
    }
    if (newEntry.amount <= 0) {
      showMessage('Jumlah harus lebih besar dari 0.', 'error');
      return;
    }
    const entryDate = new Date(newEntry.date);
    if (isNaN(entryDate.getTime())) {
        showMessage('Tanggal tidak valid.', 'error');
        return;
    }
    addFinanceEntry(newEntry);
    setNewEntry({
      date: new Date().toISOString().split('T')[0],
      type: 'revenue',
      description: '',
      amount: 0,
    });
  };

  const handleDeleteClick = (entryId) => {
    setConfirmMessage('Apakah Anda yakin ingin menghapus entri keuangan ini?');
    setConfirmAction(() => () => deleteFinanceEntry(entryId));
    setShowConfirmModal(true);
  };

  // Filter finances
  const filteredFinances = finances.filter(f => {
    const matchesType = filterType === 'all' || f.type === filterType;

    const entryDate = f.date?.toDate ? f.date.toDate() : new Date(f.date);
    let matchesStartDate = true;
    let matchesEndDate = true;

    if (filterStartDate) {
      const start = new Date(filterStartDate);
      start.setHours(0,0,0,0);
      matchesStartDate = entryDate >= start;
    }
    if (filterEndDate) {
      const end = new Date(filterEndDate);
      end.setHours(23,59,59,999);
      matchesEndDate = entryDate <= end;
    }

    return matchesType && matchesStartDate && matchesEndDate;
  });

  const totalRevenue = filteredFinances.filter(f => f.type === 'revenue').reduce((sum, f) => sum + (f.amount || 0), 0);
  const totalExpenses = filteredFinances.filter(f => f.type === 'expense').reduce((sum, f) => sum + (f.amount || 0), 0);
  const netProfit = totalRevenue - totalExpenses;

  // Sort finances by date, newest first
  const sortedFinances = [...filteredFinances].sort((a, b) => {
    const dateA = a.date?.toDate ? a.date.toDate() : new Date(a.date);
    const dateB = b.date?.toDate ? b.date.toDate() : new Date(b.date);
    return dateB - dateA;
  });

  if (!selectedHotelId) {
    return (
      <div className="p-4 sm:p-6 bg-gray-50 min-h-screen flex flex-col items-center justify-center text-center">
        <p className="text-lg text-gray-700">Silakan pilih hotel untuk melihat dan mengelola keuangan.</p>
      </div>
    );
  }

  return (
    <div className="p-4 sm:p-6 bg-gray-50 min-h-screen">
      <h1 className="text-3xl font-extrabold text-gray-900 mb-8">Laporan Keuangan</h1>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        {/* Revenue Card */}
        <div className="bg-white p-5 rounded-lg shadow-md border border-gray-100">
          <p className="text-sm font-medium text-gray-500">Total Pendapatan</p>
          <p className="text-3xl font-bold text-green-600">{formatCurrency(totalRevenue)}</p>
        </div>
        {/* Expenses Card */}
        <div className="bg-white p-5 rounded-lg shadow-md border border-gray-100">
          <p className="text-sm font-medium text-gray-500">Total Pengeluaran</p>
          <p className="text-3xl font-bold text-red-600">{formatCurrency(totalExpenses)}</p>
        </div>
        {/* Net Profit Card */}
        <div className="bg-white p-5 rounded-lg shadow-md border border-gray-100">
          <p className="text-sm font-medium text-gray-500">Laba Bersih</p>
          <p className={`text-3xl font-bold ${netProfit >= 0 ? 'text-blue-600' : 'text-orange-600'}`}>
            {formatCurrency(netProfit)}
          </p>
        </div>
      </div>

      <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100 mb-8">
        <h2 className="text-xl font-bold text-gray-800 mb-4">Tambah Entri Keuangan Baru</h2>
        <form onSubmit={handleAddEntry} className="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
          <div>
            <label htmlFor="financeDate" className="block text-sm font-medium text-gray-700 mb-1">Tanggal</label>
            <input
              type="date"
              id="financeDate"
              name="date"
              value={newEntry.date}
              onChange={handleEntryChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              required
            />
          </div>
          <div>
            <label htmlFor="financeType" className="block text-sm font-medium text-gray-700 mb-1">Tipe</label>
            <select
              id="financeType"
              name="type"
              value={newEntry.type}
              onChange={handleEntryChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="revenue">Pendapatan</option>
              <option value="expense">Pengeluaran</option>
            </select>
          </div>
          <div>
            <label htmlFor="financeDescription" className="block text-sm font-medium text-gray-700 mb-1">Deskripsi</label>
            <input
              type="text"
              id="financeDescription"
              name="description"
              value={newEntry.description}
              onChange={handleEntryChange}
              placeholder="Deskripsi entri keuangan"
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              required
            />
          </div>
          <div>
            <label htmlFor="financeAmount" className="block text-sm font-medium text-gray-700 mb-1">Jumlah</label>
            <input
              type="number"
              id="financeAmount"
              name="amount"
              value={newEntry.amount}
              onChange={handleEntryChange}
              placeholder="0"
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              min="0"
              step="0.01"
              required
            />
          </div>
          <button
            type="submit"
            className="px-6 py-3 bg-blue-600 text-white rounded-lg shadow-md hover:bg-blue-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-75 font-semibold"
          >
            Tambah Entri
          </button>
        </form>
      </div>

      <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100 mb-8">
        <h2 className="text-xl font-bold text-gray-800 mb-4">Filter Entri Keuangan</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label htmlFor="filterType" className="block text-sm font-medium text-gray-700 mb-1">Tipe</label>
            <select
              id="filterType"
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">Semua</option>
              <option value="revenue">Pendapatan</option>
              <option value="expense">Pengeluaran</option>
            </select>
          </div>
          <div>
            <label htmlFor="filterStartDate" className="block text-sm font-medium text-gray-700 mb-1">Tanggal Mulai</label>
            <input
              type="date"
              id="filterStartDate"
              value={filterStartDate}
              onChange={(e) => setFilterStartDate(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          <div>
            <label htmlFor="filterEndDate" className="block text-sm font-medium text-gray-700 mb-1">Tanggal Akhir</label>
            <input
              type="date"
              id="filterEndDate"
              value={filterEndDate}
              onChange={(e) => setFilterEndDate(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>
      </div>

      <div className="overflow-x-auto bg-white rounded-lg shadow-md border border-gray-100">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Tanggal
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Tipe
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Deskripsi
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Jumlah
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Tindakan
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {sortedFinances.length === 0 ? (
              <tr>
                <td colSpan="5" className="px-6 py-4 text-center text-gray-500">
                  Tidak ada entri keuangan yang ditemukan.
                </td>
              </tr>
            ) : (
              sortedFinances.map((finance) => (
                <tr key={finance.id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                    {formatDate(finance.date)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                      ${finance.type === 'revenue' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                      {finance.type === 'revenue' ? 'Pendapatan' : 'Pengeluaran'}
                    </span>
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-700">{finance.description}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <span className={finance.type === 'revenue' ? 'text-green-600' : 'text-red-600'}>
                      {finance.type === 'revenue' ? '+' : '-'}{formatCurrency(finance.amount)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button
                      onClick={() => handleDeleteClick(finance.id)}
                      className="text-red-600 hover:text-red-900 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-75"
                      title="Hapus Entri"
                    >
                      <i className="fas fa-trash-alt"></i>
                    </button>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default Finance;
