import React, { useState } from 'react';
import { formatCurrency } from '../utils/formatters';
import RoomModal from './modals/RoomModal';

const Rooms = ({ 
  rooms, 
  selectedHotelId,
  addRoom, 
  updateRoom, 
  deleteRoom,
  showMessage,
  setConfirmMessage,
  setConfirmAction,
  setShowConfirmModal
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterType, setFilterType] = useState('all');
  const [showRoomModal, setShowRoomModal] = useState(false);
  const [currentRoom, setCurrentRoom] = useState(null);

  const handleEdit = (room) => {
    setCurrentRoom(room);
    setShowRoomModal(true);
  };

  const handleDeleteClick = (roomId) => {
    setConfirmMessage('Apakah Anda yakin ingin menghapus kamar ini? Ini akan menghapus semua data terkait.');
    setConfirmAction(() => () => deleteRoom(roomId));
    setShowConfirmModal(true);
  };

  // Filter and search logic
  const filteredRooms = rooms.filter(room => {
    const matchesSearch = searchTerm === '' ||
      room.roomNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      room.roomType.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = filterStatus === 'all' || room.status === filterStatus;
    const matchesType = filterType === 'all' || room.roomType === filterType;

    return matchesSearch && matchesStatus && matchesType;
  });

  const sortedRooms = [...filteredRooms].sort((a,b) => parseInt(a.roomNumber) - parseInt(b.roomNumber));

  if (!selectedHotelId) {
    return (
      <div className="p-4 sm:p-6 bg-gray-50 min-h-screen flex flex-col items-center justify-center text-center">
        <p className="text-lg text-gray-700">Silakan pilih hotel untuk melihat dan mengelola kamar.</p>
      </div>
    );
  }

  return (
    <div className="p-4 sm:p-6 bg-gray-50 min-h-screen">
      <h1 className="text-3xl font-extrabold text-gray-900 mb-8">Manajemen Kamar</h1>

      <div className="flex flex-col sm:flex-row justify-between items-center mb-6 gap-4">
        <input
          type="text"
          placeholder="Cari nomor kamar atau tipe..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full sm:w-1/3 px-4 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
        <select
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value)}
          className="w-full sm:w-auto px-4 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="all">Semua Status</option>
          <option value="available">Tersedia</option>
          <option value="occupied">Terisi</option>
          <option value="maintenance">Perawatan</option>
        </select>
        <select
          value={filterType}
          onChange={(e) => setFilterType(e.target.value)}
          className="w-full sm:w-auto px-4 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="all">Semua Tipe</option>
          <option value="Standard">Standard</option>
          <option value="Double">Double</option>
          <option value="Twin">Twin</option>
          <option value="Triple">Triple</option>
          <option value="Quad">Quad</option>
          <option value="Suite">Suite</option>
        </select>
        <button
          onClick={() => { setCurrentRoom(null); setShowRoomModal(true); }}
          className="w-full sm:w-auto px-6 py-3 bg-blue-600 text-white rounded-lg shadow-md hover:bg-blue-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-75 font-semibold"
        >
          Tambah Kamar
        </button>
      </div>

      <div className="overflow-x-auto bg-white rounded-lg shadow-md border border-gray-100">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Nomor Kamar
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Tipe Kamar
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Kapasitas
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Harga per Malam
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Tindakan
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {sortedRooms.length === 0 ? (
              <tr>
                <td colSpan="6" className="px-6 py-4 text-center text-gray-500">
                  Tidak ada kamar yang ditemukan.
                </td>
              </tr>
            ) : (
              sortedRooms.map((room) => (
                <tr key={room.id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{room.roomNumber}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{room.roomType}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{room.capacity}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{formatCurrency(room.pricePerNight)}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                      ${room.status === 'available' ? 'bg-green-100 text-green-800' :
                        room.status === 'occupied' ? 'bg-red-100 text-red-800' :
                        'bg-yellow-100 text-yellow-800'
                      }`}>
                      {room.status === 'available' ? 'Tersedia' :
                       room.status === 'occupied' ? 'Terisi' :
                       'Perawatan'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium flex items-center gap-2">
                    <button
                      onClick={() => handleEdit(room)}
                      className="text-indigo-600 hover:text-indigo-900 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-opacity-75"
                      title="Edit Kamar"
                    >
                      <i className="fas fa-edit"></i>
                    </button>
                    <button
                      onClick={() => handleDeleteClick(room.id)}
                      className="text-red-600 hover:text-red-900 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-75"
                      title="Hapus Kamar"
                    >
                      <i className="fas fa-trash-alt"></i>
                    </button>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
      {showRoomModal && (
        <RoomModal
          room={currentRoom}
          onClose={() => setShowRoomModal(false)}
          onSave={(id, data) => id ? updateRoom(id, data) : addRoom(data)}
          showMessage={showMessage}
        />
      )}
    </div>
  );
};

export default Rooms;
