import React, { useCallback } from 'react';
import { formatCurrency, formatDate } from '../utils/formatters';

const Dashboard = ({ 
  rooms, 
  reservations, 
  finances, 
  hotels, 
  selectedHotelId, 
  setCurrentHotel, 
  setShowHotelModal 
}) => {
  const dashboardContent = useCallback(() => {
    const totalRooms = rooms.length;
    const occupiedRooms = rooms.filter(room => room.status === 'occupied').length;
    const availableRooms = rooms.filter(room => room.status === 'available').length;
    const occupancyRate = totalRooms > 0 ? ((occupiedRooms / totalRooms) * 100).toFixed(1) : 0;

    const today = new Date();
    today.setHours(0, 0, 0, 0); // Normalize to start of day

    const upcomingCheckIns = reservations.filter(res => {
      const checkInDate = new Date(res.checkInDate.toDate());
      checkInDate.setHours(0, 0, 0, 0);
      return checkInDate.getTime() === today.getTime() && res.status !== 'checked-out' && res.status !== 'cancelled';
    }).length;

    const upcomingCheckOuts = reservations.filter(res => {
      const checkOutDate = new Date(res.checkOutDate.toDate());
      checkOutDate.setHours(0, 0, 0, 0);
      return checkOutDate.getTime() === today.getTime() && res.status !== 'checked-out' && res.status !== 'cancelled';
    }).length;

    const totalRevenue = finances.filter(f => f.type === 'revenue').reduce((sum, f) => sum + (f.amount || 0), 0);
    const totalExpenses = finances.filter(f => f.type === 'expense').reduce((sum, f) => sum + (f.amount || 0), 0);
    const netProfit = totalRevenue - totalExpenses;

    if (!selectedHotelId && hotels.length === 0) {
      return (
        <div className="p-4 sm:p-6 bg-gray-50 min-h-screen flex flex-col items-center justify-center text-center">
          <h1 className="text-3xl font-extrabold text-gray-900 mb-4">Selamat Datang di Arrahmah Hotel Manajemen</h1>
          <p className="text-lg text-gray-700 mb-8">
            Anda belum memiliki hotel yang terdaftar. Silakan tambahkan hotel pertama Anda untuk memulai pengelolaan.
          </p>
          <button
            onClick={() => { setCurrentHotel(null); setShowHotelModal(true); }}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg shadow-md hover:bg-blue-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-75 font-semibold"
          >
            <i className="fas fa-plus mr-2"></i> Tambah Hotel Baru
          </button>
        </div>
      );
    }

    if (!selectedHotelId) {
      return (
        <div className="p-4 sm:p-6 bg-gray-50 min-h-screen flex flex-col items-center justify-center text-center">
          <h1 className="text-3xl font-extrabold text-gray-900 mb-4">Pilih Hotel untuk Memulai</h1>
          <p className="text-lg text-gray-700 mb-8">
            Silakan pilih hotel dari daftar di navigasi samping atau tambahkan hotel baru.
          </p>
          <button
            onClick={() => { setCurrentHotel(null); setShowHotelModal(true); }}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg shadow-md hover:bg-blue-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-75 font-semibold"
          >
            <i className="fas fa-plus mr-2"></i> Tambah Hotel Baru
          </button>
        </div>
      );
    }

    return (
      <div className="p-4 sm:p-6 bg-gray-50 min-h-screen">
        <h1 className="text-3xl font-extrabold text-gray-900 mb-8">Dashboard Hotel: {hotels.find(h => h.id === selectedHotelId)?.name || 'Memuat...'}</h1>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* Card: Total Kamar */}
          <div className="bg-white p-5 rounded-lg shadow-md border border-gray-100 flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Total Kamar</p>
              <p className="text-3xl font-bold text-gray-900">{totalRooms}</p>
            </div>
            <span className="text-blue-500 text-4xl">
              <i className="fas fa-bed"></i>
            </span>
          </div>

          {/* Card: Kamar Tersedia */}
          <div className="bg-white p-5 rounded-lg shadow-md border border-gray-100 flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Kamar Tersedia</p>
              <p className="text-3xl font-bold text-gray-900">{availableRooms}</p>
            </div>
            <span className="text-green-500 text-4xl">
              <i className="fas fa-door-open"></i>
            </span>
          </div>

          {/* Card: Tingkat Hunian */}
          <div className="bg-white p-5 rounded-lg shadow-md border border-gray-100 flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Tingkat Hunian</p>
              <p className="text-3xl font-bold text-gray-900">{occupancyRate}%</p>
            </div>
            <span className="text-yellow-500 text-4xl">
              <i className="fas fa-chart-pie"></i>
            </span>
          </div>

          {/* Card: Check-in Hari Ini */}
          <div className="bg-white p-5 rounded-lg shadow-md border border-gray-100 flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Check-in Hari Ini</p>
              <p className="text-3xl font-bold text-gray-900">{upcomingCheckIns}</p>
            </div>
            <span className="text-purple-500 text-4xl">
              <i className="fas fa-sign-in-alt"></i>
            </span>
          </div>

          {/* Card: Check-out Hari Ini */}
          <div className="bg-white p-5 rounded-lg shadow-md border border-gray-100 flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Check-out Hari Ini</p>
              <p className="text-3xl font-bold text-gray-900">{upcomingCheckOuts}</p>
            </div>
            <span className="text-red-500 text-4xl">
              <i className="fas fa-sign-out-alt"></i>
            </span>
          </div>

          {/* Card: Total Pendapatan */}
          <div className="bg-white p-5 rounded-lg shadow-md border border-gray-100 flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Total Pendapatan</p>
              <p className="text-2xl font-bold text-gray-900">{formatCurrency(totalRevenue)}</p>
            </div>
            <span className="text-teal-500 text-4xl">
              <i className="fas fa-money-bill-wave"></i>
            </span>
          </div>

          {/* Card: Total Pengeluaran */}
          <div className="bg-white p-5 rounded-lg shadow-md border border-gray-100 flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Total Pengeluaran</p>
              <p className="text-2xl font-bold text-gray-900">{formatCurrency(totalExpenses)}</p>
            </div>
            <span className="text-orange-500 text-4xl">
              <i className="fas fa-file-invoice-dollar"></i>
            </span>
          </div>

          {/* Card: Laba Bersih */}
          <div className="bg-white p-5 rounded-lg shadow-md border border-gray-100 flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Laba Bersih</p>
              <p className={`text-3xl font-bold ${netProfit >= 0 ? 'text-blue-600' : 'text-orange-600'}`}>
              {formatCurrency(netProfit)}
            </p>
          </div>
          <span className="text-indigo-500 text-4xl">
            <i className="fas fa-wallet"></i>
          </span>
        </div>
      </div>

        {/* Section for Quick Actions / Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100">
            <h2 className="text-xl font-bold text-gray-800 mb-4">Reservasi Mendatang</h2>
            {reservations.filter(res => {
              const checkInDate = new Date(res.checkInDate.toDate());
              checkInDate.setHours(0, 0, 0, 0);
              return checkInDate >= today && res.status !== 'checked-out' && res.status !== 'cancelled';
            }).sort((a, b) => new Date(a.checkInDate.toDate()) - new Date(b.checkInDate.toDate()))
            .slice(0, 5) // Show top 5
            .map(res => (
              <div key={res.id} className="border-b border-gray-200 py-3 last:border-b-0">
                <p className="font-semibold text-gray-800">{res.guestName} - Kamar {res.roomNumber}</p>
                <p className="text-sm text-gray-600">Check-in: {formatDate(res.checkInDate)}</p>
              </div>
            ))}
            {reservations.filter(res => new Date(res.checkInDate.toDate()) >= today && res.status !== 'checked-out' && res.status !== 'cancelled').length === 0 && (
              <p className="text-gray-500">Tidak ada reservasi mendatang.</p>
            )}
          </div>

          <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100">
            <h2 className="text-xl font-bold text-gray-800 mb-4">Ketersediaan Kamar</h2>
            <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
              {rooms.sort((a,b) => parseInt(a.roomNumber) - parseInt(b.roomNumber)).map(room => (
                <div key={room.id} className={`p-3 rounded-md text-center shadow-sm
                  ${room.status === 'available' ? 'bg-green-100 text-green-800' :
                    room.status === 'occupied' ? 'bg-red-100 text-red-800' :
                    'bg-yellow-100 text-yellow-800'
                  }`}>
                  <p className="font-bold text-lg">{room.roomNumber}</p>
                  <p className="text-sm">{room.status === 'available' ? 'Tersedia' : room.status === 'occupied' ? 'Terisi' : 'Perawatan'}</p>
                </div>
              ))}
              {rooms.length === 0 && <p className="text-gray-500 col-span-2">Tidak ada kamar terdaftar.</p>}
            </div>
          </div>
        </div>
      </div>
    );
  }, [rooms, reservations, finances, hotels, selectedHotelId, setCurrentHotel, setShowHotelModal]);

  return dashboardContent();
};

export default Dashboard;
