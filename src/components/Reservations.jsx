import React, { useState, useCallback } from 'react';
import { formatDate } from '../utils/formatters';
import ReservationModal from './modals/ReservationModal';
import ReservationDetailModal from './modals/ReservationDetailModal';

const Reservations = ({ 
  reservations, 
  rooms, 
  hotels,
  selectedHotelId,
  addReservation, 
  updateReservation, 
  deleteReservation,
  showMessage,
  setConfirmMessage,
  setConfirmAction,
  setShowConfirmModal
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [showReservationModal, setShowReservationModal] = useState(false);
  const [currentReservation, setCurrentReservation] = useState(null);
  const [showReservationDetailModal, setShowReservationDetailModal] = useState(false);

  const handleEdit = (res) => {
    setCurrentReservation(res);
    setShowReservationModal(true);
  };

  const handleDeleteClick = (resId) => {
    setConfirmMessage('Apakah Anda yakin ingin menghapus reservasi ini?');
    setConfirmAction(() => () => deleteReservation(resId));
    setShowConfirmModal(true);
  };

  const handleViewDetails = (res) => {
    setCurrentReservation(res);
    setShowReservationDetailModal(true);
  };

  // Filter and search logic
  const filteredReservations = reservations.filter(res => {
    const matchesSearch = searchTerm === '' ||
      res.guestName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      res.roomNumber.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = filterStatus === 'all' || res.status === filterStatus;

    return matchesSearch && matchesStatus;
  });

  // Sort reservations by check-in date
  const sortedReservations = [...filteredReservations].sort((a, b) => {
    const dateA = a.checkInDate?.toDate ? a.checkInDate.toDate() : new Date(a.checkInDate);
    const dateB = b.checkInDate?.toDate ? b.checkInDate.toDate() : new Date(b.checkInDate);
    return dateA - dateB;
  });

  if (!selectedHotelId) {
    return (
      <div className="p-4 sm:p-6 bg-gray-50 min-h-screen flex flex-col items-center justify-center text-center">
        <p className="text-lg text-gray-700">Silakan pilih hotel untuk melihat dan mengelola reservasi.</p>
      </div>
    );
  }

  return (
    <div className="p-4 sm:p-6 bg-gray-50 min-h-screen">
      <h1 className="text-3xl font-extrabold text-gray-900 mb-8">Manajemen Reservasi</h1>

      <div className="flex flex-col sm:flex-row justify-between items-center mb-6 gap-4">
        <input
          type="text"
          placeholder="Cari nama tamu atau nomor kamar..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full sm:w-1/2 px-4 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
        <select
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value)}
          className="w-full sm:w-auto px-4 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="all">Semua Status</option>
          <option value="confirmed">Dikonfirmasi</option>
          <option value="checked-in">Check-in</option>
          <option value="checked-out">Check-out</option>
          <option value="cancelled">Dibatalkan</option>
        </select>
        <button
          onClick={() => { setCurrentReservation(null); setShowReservationModal(true); }}
          className="w-full sm:w-auto px-6 py-3 bg-blue-600 text-white rounded-lg shadow-md hover:bg-blue-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-75 font-semibold"
        >
          Tambah Reservasi
        </button>
      </div>

      <div className="overflow-x-auto bg-white rounded-lg shadow-md border border-gray-100">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Nama Tamu
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Kamar
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Check-in
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Check-out
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Tamu
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Paket Makanan
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Tindakan
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {sortedReservations.length === 0 ? (
              <tr>
                <td colSpan="8" className="px-6 py-4 text-center text-gray-500">
                  Tidak ada reservasi yang ditemukan.
                </td>
              </tr>
            ) : (
              sortedReservations.map((res) => (
                <tr key={res.id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{res.guestName}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{res.roomNumber}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{formatDate(res.checkInDate)}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{formatDate(res.checkOutDate)}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{res.numberOfGuests}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{res.mealPlan}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                      ${res.status === 'confirmed' ? 'bg-blue-100 text-blue-800' :
                        res.status === 'checked-in' ? 'bg-green-100 text-green-800' :
                        res.status === 'checked-out' ? 'bg-gray-100 text-gray-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                      {res.status === 'confirmed' ? 'Dikonfirmasi' :
                       res.status === 'checked-in' ? 'Check-in' :
                       res.status === 'checked-out' ? 'Check-out' :
                       'Dibatalkan'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium flex items-center gap-2">
                    <button
                      onClick={() => handleViewDetails(res)}
                      className="text-blue-600 hover:text-blue-900 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-75"
                      title="Lihat Detail/Invoice"
                    >
                      <i className="fas fa-info-circle"></i>
                    </button>
                    <button
                      onClick={() => handleEdit(res)}
                      className="text-indigo-600 hover:text-indigo-900 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-opacity-75"
                      title="Edit Reservasi"
                    >
                      <i className="fas fa-edit"></i>
                    </button>
                    <button
                      onClick={() => handleDeleteClick(res.id)}
                      className="text-red-600 hover:text-red-900 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-75"
                      title="Hapus Reservasi"
                    >
                      <i className="fas fa-trash-alt"></i>
                    </button>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
      {showReservationModal && (
        <ReservationModal
          reservation={currentReservation}
          rooms={rooms}
          onClose={() => setShowReservationModal(false)}
          onSave={(id, data) => id ? updateReservation(id, data) : addReservation(data)}
          showMessage={showMessage}
        />
      )}
      {showReservationDetailModal && (
        <ReservationDetailModal
          reservation={currentReservation}
          onClose={() => setShowReservationDetailModal(false)}
          hotels={hotels}
          selectedHotelId={selectedHotelId}
        />
      )}
    </div>
  );
};

export default Reservations;
